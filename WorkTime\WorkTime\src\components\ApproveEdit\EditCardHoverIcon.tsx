import React from "react";
import styled from "styled-components";
import attentionHoverIcon from "../../assets/images/dot-icons/attention-hover.svg";
import attentionIcon from "../../assets/images/dot-icons/attention.svg";
import EditCard from "./EditCard";

interface EditCardHoverIconProps {
  newValue: string;
  cardKey: string;
  onCancel?: () => void;
  onConfirm?: () => void;
  position?: "top" | "bottom" | "left" | "right";
}

const IconContainer = styled.div`
  position: relative;
  display: inline-block;
`;

const HoverIcon = styled.div`
  width: 1.5rem;
  height: 1.5rem;
  cursor: pointer;
  background: url(${attentionIcon}) center/contain no-repeat;
  transition: all 0.2s ease;

  &:hover {
    background: url(${attentionHoverIcon}) center/contain no-repeat;
  }
`;

const CardWrapper = styled.div<{
  position: "top" | "bottom" | "left" | "right";
}>`
  position: absolute;
  z-index: 1005;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s ease, visibility 0.2s ease;
  pointer-events: none;

  ${(props) => {
    switch (props.position) {
      case "top":
        return `
          bottom: 100%;
          left: 50%;
          transform: translateX(-50%);
          padding-bottom: 0.5rem;
        `;
      case "bottom":
        return `
          top: 100%;
          left: 50%;
          transform: translateX(-50%);
          padding-top: 0.5rem;
        `;
      case "left":
        return `
          right: 100%;
          top: 50%;
          transform: translateY(-50%);
          padding-right: 0.5rem;
        `;
      case "right":
        return `
          left: 100%;
          top: 50%;
          transform: translateY(-50%);
          padding-left: 0.5rem;
        `;
      default:
        return `
          top: 100%;
          left: 50%;
          transform: translateX(-50%);
          padding-top: 0.5rem;
        `;
    }
  }}

  ${IconContainer}:hover &,
  &:hover {
    opacity: 1;
    visibility: visible;
    pointer-events: auto;
  }
`;

const EditCardHoverIcon: React.FC<EditCardHoverIconProps> = ({
  newValue,
  cardKey,
  onCancel,
  onConfirm,
  position = "bottom",
}) => {
  return (
    <IconContainer data-testid="edit-card-hover-icon">
      <HoverIcon data-testid="hover-icon" />
      <CardWrapper position={position} data-testid="card-wrapper">
        <EditCard
          newValue={newValue}
          cardKey={cardKey}
          onCancel={onCancel}
          onConfirm={onConfirm}
        />
      </CardWrapper>
    </IconContainer>
  );
};

export default EditCardHoverIcon;
