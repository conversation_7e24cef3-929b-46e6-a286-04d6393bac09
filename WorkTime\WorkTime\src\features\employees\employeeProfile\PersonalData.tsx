import { useEffect, useState } from "react";
import styled from "styled-components";
import profileMan from "../../../assets/images/profile/profileMan.svg";
import Container from "../../../components/Container";
import Fieldset from "../../../components/Fiedlset/Fieldset";
import Legend from "../../../components/Fiedlset/Legend";
import Image from "../../../components/Image";
import Button from "../../../components/Inputs/Button";
import Label from "../../../components/Inputs/Label";
import { PersonalInformationDTO } from "../../../models/DTOs/payrolls/PersonalInformationDTO";

import copyIconHover from "../../../assets/images/profile/copyIconHover.svg";
import copyIcon from "../../../assets/images/profile/copyIconNormal.svg";
import editIconHover from "../../../assets/images/profile/editHover.svg";
import editIcon from "../../../assets/images/profile/editNormal.svg";
import EditCardHoverIcon from "../../../components/ApproveEdit/EditCardHoverIcon";
import { Genders } from "../../../constants/enum";
import {
  AddressDTO,
  AddressPurpose,
} from "../../../models/DTOs/address/AddressDTO";
import Translator from "../../../services/language/Translator";
import { useMenu } from "../../MenuContext";

const FieldsetRow = styled(Container)`
  display: grid;
  grid-template-columns: 17% 25% 13% 30%;
  gap: 2rem;
  margin: 0.5rem 1rem;
`;

const StyledFieldset = styled(Fieldset)`
  border: 0.2rem solid var(--profile-fieldset-border-color);
  width: 90%;
  margin: 1rem;
  padding: 1rem;
  position: relative;

  @media (max-width: 900px) {
    width: 100%;
    margin: 1rem 0;
  }
`;

const EmployeeImage = styled(Image)`
  border-radius: 50%;
  margin: 1rem;
`;

const EmployeeName = styled(Label)`
  text-align: center;
  font-weight: bold;
  font-size: 1.4rem;
  word-wrap: normal;
  margin-top: 0.5rem;
`;

const DepartmentInfo = styled(Container)`
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 1rem;
  align-items: center;
  justify-content: center;
  position: relative;
  top: 2rem;
`;

const DepartmentName = styled(Label)`
  font-size: 1.15rem;
  margin-top: 0.2rem;
  color: var(--profile-department-name-font-color);
`;

const DepartmentLeader = styled(Label)`
  font-size: 1.05rem;
  color: var(--profile-department-leader-name-font-color);
  margin-top: 0.1rem;
`;

const ContractInfo = styled(Container)`
  display: flex;
  text-align: center;
  position: relative;
  top: 4rem;
  flex-direction: column;
`;

const LightLabel = styled(Label)`
  color: var(--profile-department-leader-name-font-color);
  font-size: 0.9rem;
`;

const ValueLabel = styled(Label)`
  font-size: 1.2rem;
` as unknown as React.FC<{ children: React.ReactNode }>;

const LeftContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 12rem;
  padding: 1rem;
  border-radius: 2.2rem;
  background-color: var(--profile-left-part-background-color);
  border: 0.3rem white solid;

  @media (max-width: 1200px) {
    width: 9rem;
    margin-left: -2.5rem;
  }
`;

const RightContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  flex: 1;

  @media (max-width: 1300px) {
    flex-direction: column;
    margin-left: 0;
    width: 100%;
  }
`;

const WrapperContainer = styled(Container)`
  display: flex;
  flex-direction: row;
  width: 100%;
  padding: 1rem;
  margin: 0 auto;

  @media (max-width: 1200px) {
    width: 90%;
  }

  @media (max-width: 1000px) {
    width: 90%;
    flex-direction: column;
    align-items: center;
  }
`;

const LabelColumn = styled(Container)`
  display: flex;
  align-items: center;
`;

const ValueColumn = styled(Container)`
  display: flex;
  align-items: center;
`;
const EditButton = styled(Button)<{
  normalImage: string;
  hoverImage: string;
  isDisabled: boolean;
}>`
  position: absolute;
  top: -1rem;
  right: 0.5rem;
  width: 2.5rem;
  height: 2.5rem;
  background-color: transparent;
  background: no-repeat center / 1.6rem url(${(p) => p.normalImage});
  cursor: pointer;
  padding: 0;

  &:hover {
    background: no-repeat center / 1.6rem url(${(p) => p.hoverImage});
  }
`;

const ButtonsContainer = styled(Container)`
  display: flex;
`;

const EditNameButton = styled(Button)<{
  normalImage: string;
  hoverImage: string;
  isDisabled?: boolean;
}>`
  width: 2.5rem;
  height: 2.5rem;
  background: no-repeat center / 1.8rem url(${(p) => p.normalImage});
  cursor: pointer;
  padding: 0;
  background-color: transparent;

  &:hover {
    background: no-repeat center / 1.8rem url(${(p) => p.hoverImage});
  }
`;

interface Props {
  incomingProfile: PersonalInformationDTO;
  employeeName: string;
}

const PersonalData = ({ incomingProfile, employeeName }: Props) => {
  const [profile, setProfile] = useState(incomingProfile);
  const [identityCardAddress, setIdentityCardAddress] =
    useState<AddressDTO | null>();
  const { toggleMenu, changeView } = useMenu();

  function formatFullAddress(address?: AddressDTO | null) {
    if (!address) return "";
    const parts = [];
    if (address.neighborhood) parts.push(`${address.neighborhood},`);
    if (address.street) parts.push(` ${address.street}`);
    if (address.block) parts.push(`, ${address.block}`);
    if (address.apartment) parts.push(`, ${address.apartment}`);
    return parts.join(" ");
  }

  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    return date
      .toLocaleDateString("en-GB", {
        day: "2-digit",
        month: "2-digit",
        year: "numeric",
      })
      .replace(/\//g, ".");
  };

  const handleEdit = (step: number) => {
    changeView("edit-employee", "other", { incomingProfile, step });
    toggleMenu();
  };

  const handleCopyPersonName = async () => {
    try {
      await navigator.clipboard.writeText(employeeName);
    } catch (err) {
      console.error("Copy failed", err);
    }
  };

  useEffect(() => {
    setProfile(incomingProfile);
  }, [incomingProfile]);

  useEffect(() => {
    setIdentityCardAddress(
      incomingProfile?.addresses?.find(
        (address) =>
          address.purpose &&
          address.purpose.identifier === AddressPurpose.IdentityCard
      ) || null
    );
  }, [incomingProfile]);

  return (
    <WrapperContainer>
      <LeftContainer data-testid="profile-left-container">
        <EmployeeImage src={profileMan} data-testid="profile-employee-image" />
        <EmployeeName data-testid="profile-employee-name">
          {employeeName}
        </EmployeeName>
        <ButtonsContainer>
          <EditNameButton
            data-testid="copy-name-button"
            normalImage={copyIcon}
            hoverImage={copyIconHover}
            onClick={handleCopyPersonName}
            label=""
            isDisabled={true}
          />
          <EditNameButton
            data-testid="edit-company-button"
            normalImage={editIcon}
            hoverImage={editIconHover}
            onClick={() => handleEdit(1)}
            label=""
            isDisabled={true}
          />
        </ButtonsContainer>
        <DepartmentInfo data-testid="profile-department-info">
          <DepartmentName data-testid="profile-department-name">
            Department Name
          </DepartmentName>
          <DepartmentLeader data-testid="profile-department-leader">
            Manager
          </DepartmentLeader>
        </DepartmentInfo>
        <ContractInfo data-testid="profile-contract-info"></ContractInfo>
      </LeftContainer>
      <RightContainer data-testid="profile-right-container">
        <StyledFieldset
          onSubmit={(e) => e.preventDefault()}
          data-testid="personal-information-fieldset"
        >
          <EditButton
            data-testid="edit-company-button"
            normalImage={editIcon}
            hoverImage={editIconHover}
            onClick={() => handleEdit(1)}
            label=""
            isDisabled={true}
          />
          <Legend data-testid="information-legend">Personal Information</Legend>
          <FieldsetRow data-testid="first-row">
            <LabelColumn>
              <LightLabel>EGN / LNCH</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>{profile?.employee?.egn}</ValueLabel>
              <EditCardHoverIcon
                newValue="test"
                cardKey="test"
                onCancel={() => {}}
                onConfirm={() => {}}
              />
            </ValueColumn>
            <LabelColumn>
              <LightLabel>IBAN</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>{profile?.iban}</ValueLabel>
            </ValueColumn>
          </FieldsetRow>
          <FieldsetRow data-testid="second-row">
            <LabelColumn>
              <LightLabel>Date of birth</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>
                {formatDate(profile?.employee?.birthDate)}
              </ValueLabel>
            </ValueColumn>
            <LabelColumn>
              <LightLabel>
                {profile?.employee?.email || profile?.employee?.email !== ""
                  ? "E-mail"
                  : "User number"}
              </LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>
                {profile?.employee?.email || profile?.employee?.email !== ""
                  ? profile?.employee?.email
                  : profile?.code}
              </ValueLabel>
            </ValueColumn>
          </FieldsetRow>
          <FieldsetRow data-testid="third-row">
            <LabelColumn>
              <LightLabel>Place of birth</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>{profile?.employee?.birthPlace}</ValueLabel>
            </ValueColumn>
            <LabelColumn>
              <LightLabel>Phone number</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>{profile?.employee?.phone}</ValueLabel>
            </ValueColumn>
          </FieldsetRow>
        </StyledFieldset>
        <StyledFieldset
          onSubmit={(e) => e.preventDefault()}
          data-testid="address-fieldset"
        >
          <Legend data-testid="address-legend">ID card details</Legend>
          <FieldsetRow data-testid="address-row">
            <EditButton
              data-testid="edit-company-button"
              normalImage={editIcon}
              hoverImage={editIconHover}
              onClick={() => handleEdit(2)}
              label=""
              isDisabled={true}
            />
            <LabelColumn>
              <LightLabel>Number</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>{profile?.employee?.idNumber}</ValueLabel>
            </ValueColumn>
            <LabelColumn>
              <LightLabel>City</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>
                {identityCardAddress?.city?.name ??
                  identityCardAddress?.cityName}
              </ValueLabel>
            </ValueColumn>
          </FieldsetRow>
          <FieldsetRow data-testid="address-row">
            <LabelColumn>
              <LightLabel>Issued on</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>
                {formatDate(profile?.employee?.idIssueDate)}
              </ValueLabel>
            </ValueColumn>
            <LabelColumn>
              <LightLabel>District</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>
                {identityCardAddress?.district?.name ??
                  identityCardAddress?.districtName}
              </ValueLabel>
            </ValueColumn>
          </FieldsetRow>
          <FieldsetRow data-testid="address-row">
            <LabelColumn>
              <LightLabel>Issued from</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>
                {profile?.employee?.idIssuedFrom ? (
                  <>
                    <Translator getString="MOI" />{" "}
                    {profile.employee.idIssuedFrom}
                  </>
                ) : null}
              </ValueLabel>
            </ValueColumn>
            <LabelColumn>
              <LightLabel>Municipality</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>
                {identityCardAddress?.municipality?.name ??
                  identityCardAddress?.municipalityName}
              </ValueLabel>
            </ValueColumn>
          </FieldsetRow>
          <FieldsetRow data-testid="address-row">
            <LabelColumn>
              <LightLabel>Citizenship</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>{profile?.employee?.citizenship}</ValueLabel>
            </ValueColumn>
            <LabelColumn>
              <LightLabel>Address</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>{formatFullAddress(identityCardAddress)}</ValueLabel>
            </ValueColumn>
          </FieldsetRow>
          <FieldsetRow data-testid="address-row">
            <LabelColumn>
              <LightLabel>Gender</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>
                {profile?.employee?.gender === Genders.Female ? (
                  <Translator getString="Female" />
                ) : profile?.employee?.gender === Genders.Male ? (
                  <Translator getString="Male" />
                ) : (
                  ""
                )}
              </ValueLabel>
            </ValueColumn>
          </FieldsetRow>
        </StyledFieldset>
      </RightContainer>
    </WrapperContainer>
  );
};

export default PersonalData;
